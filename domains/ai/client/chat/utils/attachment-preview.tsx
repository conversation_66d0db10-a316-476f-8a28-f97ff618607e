import { Typography } from '@mui/joy';
import type { Attachment } from 'ai';
import type React from 'react';
import { useState } from 'react';
import { useTRPC } from '@bika/api-caller/context';
import { Button } from '@bika/ui/button';
import { isImage } from '@bika/ui/file';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Stack, Box } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { getAttachmentDisplayPath } from '../ai-chat-message';
import { useInputStore } from '../input-store';

export interface UploadingAttachment {
  id: number;
  name: string;
  base64: string;
  contentType: string;
}

export type PreviewAttachment = Attachment & { id: string };

interface AttachmentPreviewProps {
  data:
    | {
        type: 'preview';
        attachment: PreviewAttachment;
      }
    | {
        type: 'uploading';
        attachment: UploadingAttachment;
      };
}

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
};

export const DocumentsPreview: React.FC<AttachmentPreviewProps> = (props) => {
  const {
    data: { type, attachment },
  } = props;
  const trpc = useTRPC();
  const { attachments = [], setAttachments } = useInputStore();

  const [isDeletingAttachment, setIsDeletingAttachment] = useState(false);
  const deleteAttachment = async (id: string): Promise<void> => {
    setIsDeletingAttachment(true);
    await trpc.attachment.delete.mutate({ id });
    setAttachments(attachments.filter((att) => att.id !== id));
    setIsDeletingAttachment(false);
  };

  const fileSize: number | undefined = 'size' in attachment ? (attachment.size as number) : undefined;

  return (
    <Box
      key={attachment.id}
      position="relative"
      height="64px"
      width="266px"
      sx={{
        borderRadius: '4px',
        boxSizing: 'border-box',
        background: 'var(--bg-controls)',
        border: '1px solid var(--border-default)',
        overflow: 'hidden',
      }}
    >
      {type === 'uploading' ? (
        <Button
          loading
          variant="plain"
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '32px',
            height: '32px',
            background: 'transparent !important',
          }}
        >
          {''}
        </Button>
      ) : (
        <Box
          onClick={() => {
            if (!isDeletingAttachment) {
              void deleteAttachment(attachment.id);
            }
          }}
          sx={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: 'var(--bg-controls)',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '16px',
            height: '16px',
            backgroundColor: 'var(--bg-mask)',
            cursor: isDeletingAttachment ? 'not-allowed' : 'pointer',
            '&:hover': {
              background: 'var(--bg-controls-hover)',
            },
          }}
        >
          <CloseOutlined color="var(--static)" size={8} />
        </Box>
      )}
      {/* </Box> */}

      <Stack
        direction="row"
        sx={{
          paddingLeft: '16px',
          paddingRight: '16px',
          // paddingTop: '16px',
          height: '100%',
          alignItems: 'center',
        }}
      >
        <img
          src={
            type === 'uploading' && isImage({ name: attachment.name, type: attachment.contentType })
              ? attachment.base64
              : getAttachmentDisplayPath({
                  name: attachment.name,
                  contentType: attachment.contentType,
                  url: 'url' in attachment ? attachment.url : '',
                })
          }
          alt={attachment.name}
          className="w-[32px] h-[32px] object-cover"
        />
        <Box
          sx={{
            padding: '8px',
          }}
        >
          <EllipsisText>
            <Typography
              level="b3"
              sx={{
                color: 'var(--text-primary)',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                marginBottom: '2px',
              }}
            >
              {attachment.name}
            </Typography>
          </EllipsisText>

          {fileSize != null ? (
            <Typography
              level="b4"
              sx={{
                color: 'var(--text-secondary)',
              }}
            >
              {formatFileSize(fileSize)}
            </Typography>
          ) : (
            'Unknown'
          )}
        </Box>
      </Stack>
    </Box>
  );
};

export const ImagePreview = (props: AttachmentPreviewProps) => {
  const {
    data: { type, attachment },
  } = props;
  const trpc = useTRPC();
  const { attachments = [], setAttachments } = useInputStore();

  const [isDeletingAttachment, setIsDeletingAttachment] = useState(false);
  const deleteAttachment = async (id: string) => {
    setIsDeletingAttachment(true);
    await trpc.attachment.delete.mutate({ id });
    setAttachments(attachments.filter((att) => att.id !== id));
    setIsDeletingAttachment(false);
  };

  return (
    <Box
      key={attachment.id}
      position={'relative'}
      width={'64px'}
      height={'64px'}
      sx={{
        borderRadius: '4px',
        background: 'var(--bg-controls)',
        border: '1px solid var(--border-default)',
      }}
    >
      <img
        src={
          type === 'uploading' && isImage({ name: attachment.name, type: attachment.contentType })
            ? attachment.base64
            : getAttachmentDisplayPath({
                name: attachment.name,
                contentType: attachment.contentType,
                url: 'url' in attachment ? attachment.url : '',
              })
        }
        alt={attachment.name}
        className="w-[64px] h-[64px] rounded-[4px]"
      />
      {type === 'uploading' ? (
        <Button
          loading
          variant="plain"
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '32px',
            height: '32px',
            background: 'transparent !important',
          }}
        >
          {''}
        </Button>
      ) : (
        <Box
          onClick={() => {
            if (isDeletingAttachment) return;
            deleteAttachment(attachment.id);
          }}
          sx={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            background: 'var(--bg-controls)',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '16px',
            height: '16px',
            backgroundColor: 'var(--bg-mask)',
            cursor: isDeletingAttachment ? 'not-allowed' : 'pointer',
            '&:hover': {
              background: 'var(--bg-controls-hover)',
            },
          }}
        >
          <CloseOutlined color="var(--static)" size={8} />
        </Box>
      )}
    </Box>
  );
};

export const AttachmentPreview = (props: AttachmentPreviewProps) => {
  const {
    data: { type, attachment },
  } = props;
  const trpc = useTRPC();
  const { attachments = [], setAttachments } = useInputStore();

  const [isDeletingAttachment, setIsDeletingAttachment] = useState(false);
  const deleteAttachment = async (id: string) => {
    setIsDeletingAttachment(true);
    await trpc.attachment.delete.mutate({ id });
    setAttachments(attachments.filter((att) => att.id !== id));
    setIsDeletingAttachment(false);
  };

  if (isImage({ name: attachment.name, type: attachment.contentType })) {
    return <ImagePreview {...props} />;
  }
  return <DocumentsPreview {...props} />;
};
