import { INodeIconValue } from '@bika/types/node/bo';
import { SkillsetHandler } from '../types';
import tools from './server';

const skillsetHandler: SkillsetHandler = async (ctx) => ({
  key: 'default',
  display: {
    label: 'Default',
    hidden: true,
  },
  logo: {
    type: 'PRESET',
    url: '/assets/ai/skillset/default.png',
  },
  toolsetHandler: () => tools(ctx),
});

export const CONST_DEFAULT_SKILL_NODE_ICON: INodeIconValue = {
  kind: 'avatar',
  avatar: {
    type: 'PRESET',
    url: '/assets/ai/skillset/default.png',
  },
};

export default skillsetHandler;
