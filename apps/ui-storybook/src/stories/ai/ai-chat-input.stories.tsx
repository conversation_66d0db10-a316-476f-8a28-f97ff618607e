import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { AIChatInput } from '@bika/domains/ai/client/chat/ai-chat-input';
import React, { useState } from 'react';
import { Box } from '@bika/ui/layouts';
import type { AIChatOption, AIChatContextVO } from '@bika/types/ai/vo';

// Extend AIChatModelOption to include 'disabled'
type AIChatModelOption = {
  label: string;
  value: string;
  disabled?: boolean;
};
import type { UseChatHelpers } from '@ai-sdk/react';

// Mock data for stories
const mockOptions: AIChatOption[] = [
  { label: 'General Chat', value: 'general', disabled: false },
  { label: 'Code Assistant', value: 'code', disabled: false },
  { label: 'Writing Helper', value: 'writing', disabled: false },
  { label: 'Disabled Option', value: 'disabled', disabled: true },
];

const mockModelOptions: AIChatModelOption[] = [
  { label: 'GPT-4', value: 'gpt-4', disabled: false },
  { label: 'GPT-3.5', value: 'gpt-3.5', disabled: false },
  { label: 'Claude', value: 'claude', disabled: false },
];

const mockContext: AIChatContextVO[] = [
  {
    type: 'node',
    node: {
      id: '2',
      name: 'User Database',
      type: 'DATABASE',
      children: [],
      permission: {
        privilege: 'FULL_ACCESS',
        abilities: {},
      },
      sharing: false,
      hasShareLock: false,
      hasPermissions: true,
    },
  },
];

// Example wrapper component to manage state
function AIChatInputExample(props: {
  disabled?: boolean;
  placeholder?: string;
  status?: UseChatHelpers['status'];
  isAtBottom?: boolean;
  options?: AIChatOption[];
  modelOptions?: AIChatModelOption[];
  context?: AIChatContextVO[];
  allowContextMenu?: string[];
  uploadAccept?: string;
}) {
  const [input, setInput] = useState('');

  const handleSubmit = (event: { preventDefault?: () => void }) => {
    event.preventDefault?.();
    console.log('Submitted:', input);
    alert(`Message submitted: ${input}`);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleStop = () => {
    console.log('Stopped');
    alert('Chat stopped');
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '720px', margin: '0 auto' }}>
      <AIChatInput
        input={input}
        setInput={setInput}
        onChange={handleChange}
        disabled={props.disabled || false}
        placeholder={props.placeholder}
        handleSubmit={handleSubmit}
        isAtBottom={props.isAtBottom}
        status={props.status}
        stop={handleStop}
        options={props.options}
        modelOptions={props.modelOptions}
        context={props.context}
        allowContextMenu={props.allowContextMenu}
        uploadAccept={props.uploadAccept}
        skillsetIcons={
          <div style={{ display: 'flex', gap: '4px' }}>
            <span>🎯</span>
            <span>📊</span>
          </div>
        }
      />
    </Box>
  );
}

const meta = {
  title: '@bika/ai/AIChatInput',
  component: AIChatInputExample,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'AI Chat Input component with support for text input, voice input, file uploads, and various chat options.',
      },
    },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: '100vw', height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', p: 2 }}>
        <Story />
      </Box>
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the input',
    },
    status: {
      control: 'select',
      options: ['idle', 'streaming', 'submitted', 'error'],
      description: 'Current chat status',
    },
    isAtBottom: {
      control: 'boolean',
      description: 'Whether the chat is scrolled to bottom',
    },
    uploadAccept: {
      control: 'text',
      description: 'Accepted file types for upload',
    },
  },
  args: {
    disabled: false,
    placeholder: 'Type your message here...',
    status: 'ready',
    isAtBottom: true,
    uploadAccept: 'image/png, image/jpeg, image/gif, image/webp, application/pdf',
  },
} satisfies Meta<typeof AIChatInputExample>;

export default meta;
type Story = StoryObj<typeof AIChatInputExample>;

// Default story
export const Default: Story = {};

// Story with options
export const WithOptions: Story = {
  args: {
    options: mockOptions,
  },
};

// Story with context
export const WithContext: Story = {
  args: {
    context: mockContext,
  },
};

// Story with context menu
export const WithContextMenu: Story = {
  args: {
    allowContextMenu: ['upload', 'attach'],
  },
};

// Story with all features
export const WithAllFeatures: Story = {
  args: {
    options: mockOptions,
    modelOptions: mockModelOptions,
    context: mockContext,
    allowContextMenu: ['upload', 'attach'],
  },
};

// Disabled state
export const Disabled: Story = {
  args: {
    disabled: true,
    options: mockOptions,
  },
};

// Streaming state
export const Streaming: Story = {
  args: {
    status: 'streaming',
    options: mockOptions,
  },
};

// Not at bottom (shows scroll to bottom button)
export const NotAtBottom: Story = {
  args: {
    isAtBottom: false,
    options: mockOptions,
  },
};

// Custom placeholder
export const CustomPlaceholder: Story = {
  args: {
    placeholder: 'Ask me anything about your project...',
    options: mockOptions,
  },
};

// Custom upload accept types
export const CustomUploadTypes: Story = {
  args: {
    uploadAccept: 'image/*',
    allowContextMenu: ['upload'],
  },
};

// Drag and Drop Demo Component
function DragDropDemo() {
  const [input, setInput] = useState('');

  const handleSubmit = (event: { preventDefault?: () => void }) => {
    event.preventDefault?.();
    console.log('Submitted:', input);
    alert(`Message submitted: ${input}`);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleStop = () => {
    console.log('Stopped');
    alert('Chat stopped');
  };

  // Create a mock file for demonstration
  const createMockFile = (name: string, type: string) => {
    const blob = new Blob(['mock file content'], { type });
    const file = new File([blob], name, { type });
    return file;
  };

  const handleDragStart = (e: React.DragEvent, fileName: string, fileType: string) => {
    // Create a mock file and add it to the drag data
    const mockFile = createMockFile(fileName, fileType);
    const dt = new DataTransfer();
    dt.items.add(mockFile);

    // Set the drag data
    e.dataTransfer.setData('text/plain', fileName);
    e.dataTransfer.effectAllowed = 'copy';
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '900px', margin: '0 auto', display: 'flex', gap: 3 }}>
      {/* Draggable Files Panel */}
      <Box
        sx={{
          width: '200px',
          padding: 2,
          background: 'var(--bg-surface)',
          border: '1px solid var(--border-default)',
          borderRadius: '8px',
          height: 'fit-content',
        }}
      >
        <h3 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          📁 Drag Files From Here
        </h3>
        <p style={{ margin: '0 0 16px 0', fontSize: '12px', color: 'var(--text-secondary)' }}>
          Drag any file below to the chat input to see the drag-and-drop UI in action
        </p>

        {/* Mock Files */}
        {[
          { name: 'document.pdf', type: 'application/pdf', icon: '📄' },
          { name: 'image.jpg', type: 'image/jpeg', icon: '🖼️' },
          { name: 'spreadsheet.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', icon: '📊' },
          { name: 'presentation.pptx', type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation', icon: '📽️' },
        ].map((file) => (
          <Box
            key={file.name}
            draggable
            onDragStart={(e) => handleDragStart(e, file.name, file.type)}
            sx={{
              padding: '8px 12px',
              margin: '4px 0',
              background: 'var(--bg-controls)',
              border: '1px solid var(--border-default)',
              borderRadius: '6px',
              cursor: 'grab',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontSize: '12px',
              transition: 'all 0.2s ease',
              '&:hover': {
                background: 'var(--bg-controls-hover)',
                transform: 'translateY(-1px)',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              },
              '&:active': {
                cursor: 'grabbing',
                transform: 'scale(0.98)',
              },
            }}
          >
            <span>{file.icon}</span>
            <span style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
              {file.name}
            </span>
          </Box>
        ))}

        <Box
          sx={{
            marginTop: 2,
            padding: 1,
            background: 'var(--bg-elevated)',
            borderRadius: '4px',
            fontSize: '11px',
            color: 'var(--text-secondary)',
          }}
        >
          💡 <strong>Tip:</strong> You can also use <kbd>Ctrl/Cmd + U</kbd> to open file picker
        </Box>
      </Box>

      {/* Chat Input */}
      <Box sx={{ flex: 1 }}>
        <AIChatInput
          input={input}
          setInput={setInput}
          onChange={handleChange}
          disabled={false}
          placeholder="Try dragging files from the left panel here..."
          handleSubmit={handleSubmit}
          isAtBottom={true}
          status="ready"
          stop={handleStop}
          options={mockOptions}
          context={mockContext}
          allowContextMenu={['upload', 'attach']}
          uploadAccept="image/png, image/jpeg, image/gif, image/webp, application/pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx"
        />

        {/* Instructions */}
        <Box
          sx={{
            marginTop: 2,
            padding: 2,
            background: 'var(--bg-elevated)',
            border: '1px solid var(--border-default)',
            borderRadius: '8px',
            fontSize: '13px',
          }}
        >
          <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>🎮 Interactive Demo Instructions:</h4>
          <ul style={{ margin: 0, paddingLeft: '16px' }}>
            <li>Drag any file from the left panel over the chat input</li>
            <li>Watch the visual feedback: border changes, background highlights, and drop overlay</li>
            <li>Drop the file to see it processed (simulated)</li>
            <li>Try keyboard shortcut: <kbd>Ctrl/Cmd + U</kbd> for file picker</li>
            <li>Paste images directly into the textarea</li>
          </ul>
        </Box>
      </Box>
    </Box>
  );
}

// Drag and Drop demonstration story
export const DragAndDropDemo: Story = {
  render: () => <DragDropDemo />,
  parameters: {
    docs: {
      description: {
        story: 'Interactive demonstration of the drag-and-drop file upload functionality. Drag files from the left panel to see the visual feedback in action.',
      },
    },
  },
};

// Component to simulate drag-over state
function DragOverStateDemo() {
  const [input, setInput] = useState('');
  const [showDragOver, setShowDragOver] = useState(false);

  const handleSubmit = (event: { preventDefault?: () => void }) => {
    event.preventDefault?.();
    console.log('Submitted:', input);
    alert(`Message submitted: ${input}`);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleStop = () => {
    console.log('Stopped');
    alert('Chat stopped');
  };



  return (
    <Box sx={{ width: '100%', maxWidth: '720px', margin: '0 auto' }}>
      {/* Control Panel */}
      <Box
        sx={{
          marginBottom: 3,
          padding: 2,
          background: 'var(--bg-surface)',
          border: '1px solid var(--border-default)',
          borderRadius: '8px',
          textAlign: 'center',
        }}
      >
        <h3 style={{ margin: '0 0 16px 0', fontSize: '16px' }}>🎨 Drag-Over State Simulator</h3>
        <button
          onClick={() => setShowDragOver(!showDragOver)}
          style={{
            padding: '8px 16px',
            background: showDragOver ? 'var(--brand)' : 'var(--bg-controls)',
            color: showDragOver ? 'white' : 'var(--text-primary)',
            border: '1px solid var(--border-default)',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 500,
          }}
        >
          {showDragOver ? '🟢 Drag-Over Active' : '⚪ Simulate Drag-Over'}
        </button>
        <p style={{ margin: '8px 0 0 0', fontSize: '12px', color: 'var(--text-secondary)' }}>
          Click the button above to toggle the drag-over visual state
        </p>
      </Box>

      {/* Chat Input with simulated state */}
      <Box
        sx={{
          position: 'relative',
          transition: 'all 0.2s ease-in-out',
          ...(showDragOver && {
            transform: 'scale(1.02)',
            filter: 'brightness(1.05)',
          }),
        }}
      >
        <AIChatInput
          input={input}
          setInput={setInput}
          onChange={handleChange}
          disabled={false}
          placeholder="This input shows the drag-over visual feedback..."
          handleSubmit={handleSubmit}
          isAtBottom={true}
          status="ready"
          stop={handleStop}
          options={mockOptions}
          allowContextMenu={['upload', 'attach']}
          uploadAccept="image/png, image/jpeg, image/gif, image/webp, application/pdf"
          skillsetIcons={
            <div style={{ display: 'flex', gap: '4px' }}>
              <span>🎯</span>
              <span>📊</span>
            </div>
          }
        />

        {/* Simulated drag overlay */}
        {showDragOver && (
          <Box
            sx={{
              position: 'absolute',
              top: '16px',
              left: 0,
              right: 0,
              bottom: '24px',
              background: 'color-mix(in srgb, var(--brand) 5%, transparent)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              pointerEvents: 'none',
              border: '2px dashed var(--brand)',
            }}
          >
            <Box
              sx={{
                background: 'var(--bg-surface)',
                border: '2px dashed var(--brand)',
                borderRadius: '8px',
                padding: '16px 24px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: 'var(--brand)',
                fontSize: '16px',
                fontWeight: 500,
                boxShadow: 'var(--shadow-high)',
              }}
            >
              📎 Drop files here
            </Box>
          </Box>
        )}
      </Box>

      {/* Visual State Explanation */}
      <Box
        sx={{
          marginTop: 2,
          padding: 2,
          background: 'var(--bg-elevated)',
          border: '1px solid var(--border-default)',
          borderRadius: '8px',
          fontSize: '13px',
        }}
      >
        <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>🎨 Visual Feedback Elements:</h4>
        <ul style={{ margin: 0, paddingLeft: '16px' }}>
          <li><strong>Border:</strong> Changes from default to dashed brand color</li>
          <li><strong>Background:</strong> Subtle brand color tint with glow effect</li>
          <li><strong>Scale:</strong> Slight scale-up (1.02x) with brightness increase</li>
          <li><strong>Overlay:</strong> "Drop files here" message with file icon</li>
          <li><strong>Transitions:</strong> Smooth 0.2s ease-in-out animations</li>
        </ul>
      </Box>
    </Box>
  );
}

// Visual state demonstration story
export const DragOverVisualState: Story = {
  render: () => <DragOverStateDemo />,
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the visual feedback states during drag-and-drop operations. Use the toggle button to see the drag-over styling.',
      },
    },
  },
};

// Component wrapper for stories with pre-populated attachments
function AIChatInputWithAttachments(props: {
  disabled?: boolean;
  placeholder?: string;
  status?: UseChatHelpers['status'];
  isAtBottom?: boolean;
  options?: AIChatOption[];
  modelOptions?: AIChatModelOption[];
  context?: AIChatContextVO[];
  allowContextMenu?: string[];
  uploadAccept?: string;
  initialAttachments?: Array<{
    id: string;
    name: string;
    contentType: string;
    url: string;
  }>;
}) {
  const [input, setInput] = useState('');

  // Mock the input store with initial attachments
  React.useEffect(() => {
    if (props.initialAttachments) {
      // Import the store dynamically to avoid circular dependencies
      import('@bika/domains/ai/client/chat/input-store').then(({ useInputStore }) => {
        const { setAttachments } = useInputStore.getState();
        setAttachments(props.initialAttachments || []);
      });
    }

    // Cleanup on unmount
    return () => {
      import('@bika/domains/ai/client/chat/input-store').then(({ useInputStore }) => {
        const { clearAttachments } = useInputStore.getState();
        clearAttachments();
      });
    };
  }, [props.initialAttachments]);

  const handleSubmit = (event: { preventDefault?: () => void }) => {
    event.preventDefault?.();
    console.log('Submitted:', input);
    alert(`Message submitted: ${input}`);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  const handleStop = () => {
    console.log('Stopped');
    alert('Chat stopped');
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '720px', margin: '0 auto' }}>
      <AIChatInput
        input={input}
        setInput={setInput}
        onChange={handleChange}
        disabled={props.disabled || false}
        placeholder={props.placeholder}
        handleSubmit={handleSubmit}
        isAtBottom={props.isAtBottom}
        status={props.status}
        stop={handleStop}
        options={props.options}
        modelOptions={props.modelOptions}
        context={props.context}
        allowContextMenu={props.allowContextMenu}
        uploadAccept={props.uploadAccept}
        skillsetIcons={
          <div style={{ display: 'flex', gap: '4px' }}>
            <span>🎯</span>
            <span>📊</span>
          </div>
        }
      />
    </Box>
  );
}

// Mock attachment data
const mockAttachments2 = [
  {
    id: 'att-1',
    name: 'project-proposal.pdf',
    contentType: 'application/pdf',
    url: 'https://example.com/files/project-proposal.pdf',
  },
  {
    id: 'att-2',
    name: 'design-mockup.png',
    contentType: 'image/png',
    url: 'https://example.com/images/design-mockup.png',
  },
];

const mockAttachments10 = [
  {
    id: 'att-1',
    name: 'project-proposal.pdf',
    contentType: 'application/pdf',
    url: 'https://example.com/files/project-proposal.pdf',
  },
  {
    id: 'att-2',
    name: 'design-mockup.png',
    contentType: 'image/png',
    url: 'https://example.com/images/design-mockup.png',
  },
  {
    id: 'att-3',
    name: 'budget-spreadsheet.xlsx',
    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    url: 'https://example.com/files/budget-spreadsheet.xlsx',
  },
  {
    id: 'att-4',
    name: 'presentation.pptx',
    contentType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    url: 'https://example.com/files/presentation.pptx',
  },
  {
    id: 'att-5',
    name: 'team-photo.jpg',
    contentType: 'image/jpeg',
    url: 'https://example.com/images/team-photo.jpg',
  },
  {
    id: 'att-6',
    name: 'requirements.docx',
    contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    url: 'https://example.com/files/requirements.docx',
  },
  {
    id: 'att-7',
    name: 'wireframes.sketch',
    contentType: 'application/octet-stream',
    url: 'https://example.com/files/wireframes.sketch',
  },
  {
    id: 'att-8',
    name: 'logo-variants.zip',
    contentType: 'application/zip',
    url: 'https://example.com/files/logo-variants.zip',
  },
  {
    id: 'att-9',
    name: 'user-research.pdf',
    contentType: 'application/pdf',
    url: 'https://example.com/files/user-research.pdf',
  },
  {
    id: 'att-10',
    name: 'prototype-demo.mp4',
    contentType: 'video/mp4',
    url: 'https://example.com/videos/prototype-demo.mp4',
  },
];

// Story with 2 uploaded attachments
export const TwoUploadedAttachments: Story = {
  render: () => (
    <AIChatInputWithAttachments
      placeholder="Type your message here... (2 files already attached)"
      options={mockOptions}
      allowContextMenu={['upload', 'attach']}
      initialAttachments={mockAttachments2}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows the AI chat input component with 2 uploaded attachments already present. Demonstrates how the component displays multiple file attachments.',
      },
    },
  },
};

// Story with 10 uploaded attachments
export const TenUploadedAttachments: Story = {
  render: () => (
    <AIChatInputWithAttachments
      placeholder="Type your message here... (10 files already attached)"
      options={mockOptions}
      allowContextMenu={['upload', 'attach']}
      initialAttachments={mockAttachments10}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows the AI chat input component with 10 uploaded attachments already present. Demonstrates how the component handles and displays a larger number of attachments.',
      },
    },
  },
};
