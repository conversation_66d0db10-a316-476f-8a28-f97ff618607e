# AI Chat Input Stories - Attachment Testing

## New Stories Added

### TwoUploadedAttachments
- **Purpose**: Shows the AI chat input component with exactly 2 uploaded attachments
- **Mock Data**: 
  - project-proposal.pdf (PDF file)
  - design-mockup.png (PNG image)
- **Features Demonstrated**:
  - Multiple file type display
  - Attachment preview functionality
  - File size display
  - Remove attachment functionality

### TenUploadedAttachments  
- **Purpose**: Shows the AI chat input component with exactly 10 uploaded attachments
- **Mock Data**: Various file types including:
  - PDF documents
  - Images (PNG, JPG)
  - Office documents (XLSX, PPTX, DOCX)
  - Archive files (ZIP)
  - Video files (MP4)
  - Other file types (Sketch)
- **Features Demonstrated**:
  - Handling large numbers of attachments
  - Scrolling/layout behavior with many files
  - Performance with multiple attachments
  - Visual organization of attachment list

## Implementation Details

### AIChatInputWithAttachments Component
- Custom wrapper component for stories with pre-populated attachments
- Uses dynamic import to avoid circular dependencies
- <PERSON><PERSON>ly manages input store state
- Includes cleanup on unmount
- Accepts `initialAttachments` prop for mock data

### Mock Data Structure
Each attachment follows the PreviewAttachment interface:
```typescript
{
  id: string;
  name: string;
  contentType: string;
  url: string;
}
```

### Integration with Input Store
- Uses `useInputStore` from the AI chat system
- Dynamically imports store to avoid dependency issues
- Sets attachments on component mount
- Clears attachments on component unmount

## Testing Instructions

1. Navigate to Storybook at http://localhost:6007
2. Go to @bika/ai/AIChatInput section
3. Select "Two Uploaded Attachments" story
4. Verify 2 attachments are displayed
5. Select "Ten Uploaded Attachments" story  
6. Verify 10 attachments are displayed
7. Test attachment removal functionality
8. Verify file type icons and names display correctly
